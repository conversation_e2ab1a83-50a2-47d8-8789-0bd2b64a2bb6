{"name": "zaira-backend", "version": "1.0.0", "description": "Professional Node.js backend for Zaira E-Bulletin Board System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.js", "db:migrate": "node src/database/migrations/migrate.js", "db:seed": "node src/database/seeders/seed.js", "db:test": "node scripts/test-db-connection.js", "holidays:sync": "node scripts/sync-holidays.js", "holidays:sync:current": "node scripts/sync-holidays.js $(date +%Y)", "holidays:sync:next": "node scripts/sync-holidays.js $(($(date +%Y) + 1))", "holidays:setup": "node scripts/sync-holidays.js $(date +%Y) && node scripts/sync-holidays.js $(($(date +%Y) + 1))", "build": "npm run lint && npm run test", "docker:build": "docker build -t zaira-backend .", "docker:run": "docker run -p 3000:3000 zaira-backend"}, "keywords": ["nodejs", "express", "mysql", "jwt", "bulletin-board", "api", "backend"], "author": "Zaira Project Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@babel/preset-env": "^7.27.2", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "http-proxy-middleware": "^3.0.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}