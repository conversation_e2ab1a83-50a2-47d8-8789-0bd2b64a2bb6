const express = require('express');
const HolidayController = require('../controllers/HolidayController');
const { authenticateToken, requireRole } = require('../middleware/auth');
const { validateRequest } = require('../middleware/validation');
const { body, param, query } = require('express-validator');

const router = express.Router();

// All holiday routes require admin authentication
router.use(authenticateToken);
router.use(requireRole('admin'));

/**
 * @route GET /api/holidays/status/:year
 * @desc Get holiday sync status for a specific year
 * @access Admin only
 */
router.get('/status/:year', 
  [
    param('year')
      .isInt({ min: 2020, max: 2040 })
      .withMessage('Year must be a valid integer between 2020 and 2040')
  ],
  validateRequest,
  HolidayController.getHolidaySyncStatus
);

/**
 * @route POST /api/holidays/sync/:year
 * @desc Sync holidays for a specific year
 * @access Admin only
 */
router.post('/sync/:year',
  [
    param('year')
      .isInt({ min: 2020, max: 2040 })
      .withMessage('Year must be a valid integer between 2020 and 2040'),
    body('includePhilippine')
      .optional()
      .isBoolean()
      .withMessage('includePhilippine must be a boolean'),
    body('includeInternational')
      .optional()
      .isBoolean()
      .withMessage('includeInternational must be a boolean'),
    body('clearExisting')
      .optional()
      .isBoolean()
      .withMessage('clearExisting must be a boolean')
  ],
  validateRequest,
  HolidayController.syncHolidays
);

/**
 * @route GET /api/holidays/:year
 * @desc Get holidays for a specific year with pagination and filters
 * @access Admin only
 */
router.get('/:year',
  [
    param('year')
      .isInt({ min: 2020, max: 2040 })
      .withMessage('Year must be a valid integer between 2020 and 2040'),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('holiday_type')
      .optional()
      .isIn(['local', 'international', 'school'])
      .withMessage('Holiday type must be local, international, or school'),
    query('is_active')
      .optional()
      .isIn(['0', '1'])
      .withMessage('is_active must be 0 or 1')
  ],
  validateRequest,
  HolidayController.getHolidays
);

/**
 * @route PATCH /api/holidays/:holidayId/status
 * @desc Toggle holiday active status
 * @access Admin only
 */
router.patch('/:holidayId/status',
  [
    param('holidayId')
      .isInt({ min: 1 })
      .withMessage('Holiday ID must be a positive integer'),
    body('is_active')
      .isBoolean()
      .withMessage('is_active is required and must be a boolean')
  ],
  validateRequest,
  HolidayController.toggleHolidayStatus
);

/**
 * @route DELETE /api/holidays/:year
 * @desc Clear holidays for a specific year
 * @access Admin only
 */
router.delete('/:year',
  [
    param('year')
      .isInt({ min: 2020, max: 2040 })
      .withMessage('Year must be a valid integer between 2020 and 2040'),
    body('holiday_type')
      .optional()
      .isIn(['local', 'international', 'school'])
      .withMessage('Holiday type must be local, international, or school')
  ],
  validateRequest,
  HolidayController.clearHolidays
);

/**
 * @route GET /api/holidays/statistics
 * @desc Get holiday statistics for multiple years
 * @access Admin only
 */
router.get('/statistics',
  HolidayController.getHolidayStatistics
);

/**
 * @route GET /api/holidays/cron/status
 * @desc Get holiday cron job status
 * @access Admin only
 */
router.get('/cron/status',
  HolidayController.getCronStatus
);

/**
 * @route POST /api/holidays/cron/trigger
 * @desc Trigger manual holiday sync
 * @access Admin only
 */
router.post('/cron/trigger',
  [
    body('type')
      .optional()
      .isIn(['annual', 'monthly', 'initial'])
      .withMessage('Type must be annual, monthly, or initial')
  ],
  validateRequest,
  HolidayController.triggerManualSync
);

/**
 * @route POST /api/holidays/setup
 * @desc Perform initial holiday system setup
 * @access Admin only
 */
router.post('/setup',
  HolidayController.performInitialSetup
);

module.exports = router;
