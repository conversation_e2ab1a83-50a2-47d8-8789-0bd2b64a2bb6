const cron = require('cron');
const holidayService = require('./HolidayService');
const logger = require('../utils/logger');

/**
 * Holiday Cron Service
 * Manages automated holiday synchronization using cron jobs
 */
class HolidayCronService {
  constructor() {
    this.jobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the cron service
   */
  initialize() {
    if (this.isInitialized) {
      logger.warn('Holiday cron service already initialized');
      return;
    }

    try {
      // Schedule annual holiday sync for next year
      this.scheduleAnnualSync();
      
      // Schedule monthly check for missing holidays
      this.scheduleMonthlyCheck();
      
      this.isInitialized = true;
      logger.info('Holiday cron service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize holiday cron service:', error);
      throw error;
    }
  }

  /**
   * Schedule annual holiday sync
   * Runs on January 1st at 2:00 AM to sync holidays for the new year
   */
  scheduleAnnualSync() {
    const cronPattern = '0 2 1 1 *'; // January 1st at 2:00 AM
    
    const job = new cron.CronJob(
      cronPattern,
      async () => {
        await this.performAnnualSync();
      },
      null, // onComplete
      false, // start immediately
      'UTC' // timezone
    );

    this.jobs.set('annualSync', job);
    job.start();
    
    logger.info('Annual holiday sync scheduled for January 1st at 2:00 AM UTC');
  }

  /**
   * Schedule monthly check for missing holidays
   * Runs on the 1st of each month at 3:00 AM
   */
  scheduleMonthlyCheck() {
    const cronPattern = '0 3 1 * *'; // 1st of each month at 3:00 AM
    
    const job = new cron.CronJob(
      cronPattern,
      async () => {
        await this.performMonthlyCheck();
      },
      null, // onComplete
      false, // start immediately
      'UTC' // timezone
    );

    this.jobs.set('monthlyCheck', job);
    job.start();
    
    logger.info('Monthly holiday check scheduled for 1st of each month at 3:00 AM UTC');
  }

  /**
   * Perform annual holiday sync
   */
  async performAnnualSync() {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    logger.info(`Starting annual holiday sync for ${nextYear}`);
    
    try {
      // Sync holidays for next year
      const results = await holidayService.syncHolidays(nextYear, {
        includePhilippine: true,
        includeInternational: true,
        clearExisting: true
      });
      
      logger.info(`Annual holiday sync completed for ${nextYear}:`, {
        philippineHolidays: results.philippineHolidays,
        internationalHolidays: results.internationalHolidays,
        totalInserted: results.totalInserted,
        errors: results.errors
      });
      
      // Also sync current year if not already synced
      const currentYearStatus = await holidayService.getHolidaySyncStatus(currentYear);
      if (!currentYearStatus.isSynced) {
        logger.info(`Current year ${currentYear} not synced, syncing now...`);
        
        const currentYearResults = await holidayService.syncHolidays(currentYear, {
          includePhilippine: true,
          includeInternational: true,
          clearExisting: false // Don't clear existing holidays for current year
        });
        
        logger.info(`Current year holiday sync completed for ${currentYear}:`, {
          philippineHolidays: currentYearResults.philippineHolidays,
          internationalHolidays: currentYearResults.internationalHolidays,
          totalInserted: currentYearResults.totalInserted,
          errors: currentYearResults.errors
        });
      }
      
    } catch (error) {
      logger.error(`Annual holiday sync failed for ${nextYear}:`, error);
    }
  }

  /**
   * Perform monthly check for missing holidays
   */
  async performMonthlyCheck() {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    logger.info('Starting monthly holiday check');
    
    try {
      // Check current year
      await this.checkYearHolidays(currentYear);
      
      // Check next year
      await this.checkYearHolidays(nextYear);
      
      logger.info('Monthly holiday check completed');
    } catch (error) {
      logger.error('Monthly holiday check failed:', error);
    }
  }

  /**
   * Check holidays for a specific year
   */
  async checkYearHolidays(year) {
    try {
      const status = await holidayService.getHolidaySyncStatus(year);
      
      if (!status.isSynced) {
        logger.info(`No holidays found for ${year}, syncing...`);
        
        const results = await holidayService.syncHolidays(year, {
          includePhilippine: true,
          includeInternational: true,
          clearExisting: false
        });
        
        logger.info(`Holiday sync completed for ${year}:`, {
          philippineHolidays: results.philippineHolidays,
          internationalHolidays: results.internationalHolidays,
          totalInserted: results.totalInserted,
          errors: results.errors
        });
      } else {
        logger.debug(`Holidays already synced for ${year} (${status.totalHolidays} holidays)`);
      }
    } catch (error) {
      logger.error(`Failed to check holidays for ${year}:`, error);
    }
  }

  /**
   * Manually trigger annual sync (for testing or manual execution)
   */
  async triggerAnnualSync() {
    logger.info('Manually triggering annual holiday sync');
    await this.performAnnualSync();
  }

  /**
   * Manually trigger monthly check (for testing or manual execution)
   */
  async triggerMonthlyCheck() {
    logger.info('Manually triggering monthly holiday check');
    await this.performMonthlyCheck();
  }

  /**
   * Stop a specific cron job
   */
  stopJob(jobName) {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      this.jobs.delete(jobName);
      logger.info(`Stopped cron job: ${jobName}`);
    } else {
      logger.warn(`Cron job not found: ${jobName}`);
    }
  }

  /**
   * Stop all cron jobs
   */
  stopAllJobs() {
    for (const [jobName, job] of this.jobs) {
      job.stop();
      logger.info(`Stopped cron job: ${jobName}`);
    }
    this.jobs.clear();
    this.isInitialized = false;
    logger.info('All holiday cron jobs stopped');
  }

  /**
   * Get status of all cron jobs
   */
  getJobsStatus() {
    const status = {};
    
    for (const [jobName, job] of this.jobs) {
      status[jobName] = {
        running: job.running,
        nextDate: job.nextDate() ? job.nextDate().toISOString() : null,
        lastDate: job.lastDate() ? job.lastDate().toISOString() : null
      };
    }
    
    return {
      initialized: this.isInitialized,
      totalJobs: this.jobs.size,
      jobs: status
    };
  }

  /**
   * Sync holidays for current and next year (one-time setup)
   */
  async performInitialSync() {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    logger.info('Starting initial holiday sync for current and next year');
    
    try {
      // Sync current year
      const currentYearResults = await holidayService.syncHolidays(currentYear, {
        includePhilippine: true,
        includeInternational: true,
        clearExisting: true
      });
      
      logger.info(`Initial sync completed for ${currentYear}:`, {
        philippineHolidays: currentYearResults.philippineHolidays,
        internationalHolidays: currentYearResults.internationalHolidays,
        totalInserted: currentYearResults.totalInserted
      });
      
      // Sync next year
      const nextYearResults = await holidayService.syncHolidays(nextYear, {
        includePhilippine: true,
        includeInternational: true,
        clearExisting: true
      });
      
      logger.info(`Initial sync completed for ${nextYear}:`, {
        philippineHolidays: nextYearResults.philippineHolidays,
        internationalHolidays: nextYearResults.internationalHolidays,
        totalInserted: nextYearResults.totalInserted
      });
      
      logger.info('Initial holiday sync completed successfully');
      
      return {
        currentYear: currentYearResults,
        nextYear: nextYearResults
      };
    } catch (error) {
      logger.error('Initial holiday sync failed:', error);
      throw error;
    }
  }
}

// Create and export service instance
const holidayCronService = new HolidayCronService();
module.exports = holidayCronService;
