import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  RefreshCw, 
  Download, 
  Trash2, 
  Settings, 
  Globe, 
  MapPin,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { holidayService } from '../../services/holidayService';

interface Holiday {
  calendar_id: number;
  title: string;
  description: string;
  event_date: string;
  end_date: string;
  is_active: boolean;
  is_published: boolean;
  is_holiday: boolean;
  holiday_type: 'local' | 'international' | 'school';
  country_code?: string;
  is_auto_generated: boolean;
  api_source?: string;
  local_name?: string;
  holiday_types?: string;
  is_global?: boolean;
  is_fixed?: boolean;
  created_at: string;
  updated_at: string;
}

interface HolidaySyncStatus {
  year: number;
  lastSync: string | null;
  totalHolidays: number;
  philippineHolidays: number;
  internationalHolidays: number;
  schoolHolidays: number;
  isSynced: boolean;
}

const HolidayManagement: React.FC = () => {
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [syncStatus, setSyncStatus] = useState<HolidaySyncStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedHolidayType, setSelectedHolidayType] = useState<string>('all');
  const [selectedActiveStatus, setSelectedActiveStatus] = useState<string>('all');

  // Load holiday data
  const loadHolidays = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      if (selectedHolidayType !== 'all') {
        params.append('holiday_type', selectedHolidayType);
      }
      if (selectedActiveStatus !== 'all') {
        params.append('is_active', selectedActiveStatus === 'active' ? '1' : '0');
      }
      
      const response = await holidayService.getHolidays(currentYear, params.toString());
      setHolidays(response.data.holidays);
    } catch (err: any) {
      setError(err.message || 'Failed to load holidays');
    } finally {
      setLoading(false);
    }
  };

  // Load sync status
  const loadSyncStatus = async () => {
    try {
      const response = await holidayService.getHolidaySyncStatus(currentYear);
      setSyncStatus(response.data);
    } catch (err: any) {
      console.error('Failed to load sync status:', err);
    }
  };

  // Sync holidays
  const handleSyncHolidays = async (options: {
    includePhilippine?: boolean;
    includeInternational?: boolean;
    clearExisting?: boolean;
  } = {}) => {
    setSyncing(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await holidayService.syncHolidays(currentYear, {
        includePhilippine: options.includePhilippine ?? true,
        includeInternational: options.includeInternational ?? true,
        clearExisting: options.clearExisting ?? true
      });
      
      setSuccess(`Successfully synced ${response.data.totalInserted} holidays for ${currentYear}`);
      await loadHolidays();
      await loadSyncStatus();
    } catch (err: any) {
      setError(err.message || 'Failed to sync holidays');
    } finally {
      setSyncing(false);
    }
  };

  // Toggle holiday status
  const handleToggleHolidayStatus = async (holidayId: number, isActive: boolean) => {
    try {
      await holidayService.toggleHolidayStatus(holidayId, isActive);
      setSuccess(`Holiday ${isActive ? 'enabled' : 'disabled'} successfully`);
      await loadHolidays();
    } catch (err: any) {
      setError(err.message || 'Failed to update holiday status');
    }
  };

  // Clear holidays
  const handleClearHolidays = async (holidayType?: string) => {
    if (!confirm(`Are you sure you want to clear ${holidayType || 'all'} holidays for ${currentYear}?`)) {
      return;
    }
    
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const response = await holidayService.clearHolidays(currentYear, holidayType);
      setSuccess(`Cleared ${response.data.clearedCount} holidays for ${currentYear}`);
      await loadHolidays();
      await loadSyncStatus();
    } catch (err: any) {
      setError(err.message || 'Failed to clear holidays');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when year/filters change
  useEffect(() => {
    loadHolidays();
    loadSyncStatus();
  }, [currentYear, selectedHolidayType, selectedActiveStatus]);

  // Auto-dismiss messages
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 8000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const getHolidayIcon = (holiday: Holiday) => {
    if (holiday.holiday_type === 'local') return '🇵🇭';
    if (holiday.holiday_type === 'international') return '🌍';
    return '🎉';
  };

  const getHolidayTypeColor = (type: string) => {
    switch (type) {
      case 'local': return '#dc2626';
      case 'international': return '#7c3aed';
      case 'school': return '#059669';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem' 
      }}>
        <div>
          <h1 style={{ 
            fontSize: '2rem', 
            fontWeight: 'bold', 
            color: '#1f2937', 
            margin: '0 0 0.5rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <Calendar size={32} />
            Holiday Management
          </h1>
          <p style={{ color: '#6b7280', margin: 0 }}>
            Manage automatic holiday synchronization and display settings
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <select
            value={currentYear}
            onChange={(e) => setCurrentYear(parseInt(e.target.value))}
            style={{
              padding: '0.5rem 1rem',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '1rem'
            }}
          >
            {Array.from({ length: 11 }, (_, i) => {
              const year = new Date().getFullYear() - 5 + i;
              return (
                <option key={year} value={year}>
                  {year}
                </option>
              );
            })}
          </select>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div style={{
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          color: '#dc2626',
          padding: '1rem',
          borderRadius: '6px',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <AlertTriangle size={20} />
          {error}
        </div>
      )}

      {success && (
        <div style={{
          backgroundColor: '#f0fdf4',
          border: '1px solid #bbf7d0',
          color: '#16a34a',
          padding: '1rem',
          borderRadius: '6px',
          marginBottom: '1rem',
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          <CheckCircle size={20} />
          {success}
        </div>
      )}

      {/* Sync Status Card */}
      {syncStatus && (
        <div style={{
          backgroundColor: '#f9fafb',
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          padding: '1.5rem',
          marginBottom: '2rem'
        }}>
          <h3 style={{ 
            fontSize: '1.25rem', 
            fontWeight: '600', 
            color: '#1f2937', 
            margin: '0 0 1rem 0',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            <Info size={20} />
            Sync Status for {currentYear}
          </h3>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937' }}>
                {syncStatus.totalHolidays}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>Total Holidays</div>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#dc2626' }}>
                {syncStatus.philippineHolidays}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>🇵🇭 Philippine</div>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#7c3aed' }}>
                {syncStatus.internationalHolidays}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>🌍 International</div>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#059669' }}>
                {syncStatus.schoolHolidays}
              </div>
              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>🏫 School</div>
            </div>
          </div>
          
          {syncStatus.lastSync && (
            <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
              Last synced: {new Date(syncStatus.lastSync).toLocaleString()}
            </p>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        marginBottom: '2rem',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={() => handleSyncHolidays()}
          disabled={syncing}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: syncing ? '#9ca3af' : '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: syncing ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}
        >
          <RefreshCw size={16} className={syncing ? 'animate-spin' : ''} />
          {syncing ? 'Syncing...' : 'Sync All Holidays'}
        </button>

        <button
          onClick={() => handleSyncHolidays({ includePhilippine: true, includeInternational: false })}
          disabled={syncing}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: syncing ? '#9ca3af' : '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: syncing ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}
        >
          <MapPin size={16} />
          Sync Philippine Only
        </button>

        <button
          onClick={() => handleSyncHolidays({ includePhilippine: false, includeInternational: true })}
          disabled={syncing}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: syncing ? '#9ca3af' : '#7c3aed',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: syncing ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}
        >
          <Globe size={16} />
          Sync International Only
        </button>

        <button
          onClick={() => handleClearHolidays()}
          disabled={loading}
          style={{
            padding: '0.75rem 1.5rem',
            backgroundColor: loading ? '#9ca3af' : '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: loading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '500'
          }}
        >
          <Trash2 size={16} />
          Clear All Holidays
        </button>
      </div>

      {/* Filters */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        marginBottom: '2rem',
        alignItems: 'center',
        flexWrap: 'wrap'
      }}>
        <div>
          <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginRight: '0.5rem' }}>
            Holiday Type:
          </label>
          <select
            value={selectedHolidayType}
            onChange={(e) => setSelectedHolidayType(e.target.value)}
            style={{
              padding: '0.5rem',
              border: '1px solid #d1d5db',
              borderRadius: '4px',
              fontSize: '0.875rem'
            }}
          >
            <option value="all">All Types</option>
            <option value="local">🇵🇭 Philippine</option>
            <option value="international">🌍 International</option>
            <option value="school">🏫 School</option>
          </select>
        </div>

        <div>
          <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', marginRight: '0.5rem' }}>
            Status:
          </label>
          <select
            value={selectedActiveStatus}
            onChange={(e) => setSelectedActiveStatus(e.target.value)}
            style={{
              padding: '0.5rem',
              border: '1px solid #d1d5db',
              borderRadius: '4px',
              fontSize: '0.875rem'
            }}
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>

        <button
          onClick={loadHolidays}
          disabled={loading}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f3f4f6',
            color: '#374151',
            border: '1px solid #d1d5db',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem'
          }}
        >
          <RefreshCw size={14} />
          Refresh
        </button>
      </div>

      {/* Holiday List */}
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '1rem 1.5rem',
          backgroundColor: '#f9fafb',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <h3 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#1f2937',
            margin: 0
          }}>
            Holidays for {currentYear} ({holidays.length})
          </h3>
        </div>

        {loading ? (
          <div style={{ padding: '3rem', textAlign: 'center', color: '#6b7280' }}>
            <RefreshCw size={24} className="animate-spin" style={{ margin: '0 auto 1rem' }} />
            Loading holidays...
          </div>
        ) : holidays.length === 0 ? (
          <div style={{ padding: '3rem', textAlign: 'center', color: '#6b7280' }}>
            <Calendar size={48} style={{ margin: '0 auto 1rem', opacity: 0.5 }} />
            <p style={{ margin: 0, fontSize: '1.125rem' }}>No holidays found for {currentYear}</p>
            <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.875rem' }}>
              Try syncing holidays or adjusting your filters
            </p>
          </div>
        ) : (
          <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
            {holidays.map((holiday) => (
              <div
                key={holiday.calendar_id}
                style={{
                  padding: '1rem 1.5rem',
                  borderBottom: '1px solid #f3f4f6',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.5rem' }}>
                    <span style={{ fontSize: '1.25rem' }}>
                      {getHolidayIcon(holiday)}
                    </span>
                    <h4 style={{
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: '#1f2937',
                      margin: 0
                    }}>
                      {holiday.title}
                    </h4>
                    <span
                      style={{
                        padding: '0.125rem 0.5rem',
                        backgroundColor: getHolidayTypeColor(holiday.holiday_type) + '20',
                        color: getHolidayTypeColor(holiday.holiday_type),
                        borderRadius: '12px',
                        fontSize: '0.75rem',
                        fontWeight: '500'
                      }}
                    >
                      {holiday.holiday_type}
                    </span>
                    {holiday.is_auto_generated && (
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        backgroundColor: '#f3f4f6',
                        color: '#6b7280',
                        borderRadius: '12px',
                        fontSize: '0.75rem'
                      }}>
                        Auto-generated
                      </span>
                    )}
                  </div>

                  <div style={{ display: 'flex', gap: '1rem', fontSize: '0.875rem', color: '#6b7280' }}>
                    <span>📅 {new Date(holiday.event_date).toLocaleDateString()}</span>
                    {holiday.local_name && holiday.local_name !== holiday.title && (
                      <span>🏷️ {holiday.local_name}</span>
                    )}
                    {holiday.country_code && (
                      <span>🌍 {holiday.country_code}</span>
                    )}
                  </div>
                </div>

                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  {holiday.is_auto_generated ? (
                    <button
                      onClick={() => handleToggleHolidayStatus(holiday.calendar_id, !holiday.is_active)}
                      style={{
                        padding: '0.5rem',
                        backgroundColor: holiday.is_active ? '#dcfce7' : '#fef2f2',
                        color: holiday.is_active ? '#16a34a' : '#dc2626',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem',
                        fontSize: '0.75rem',
                        fontWeight: '500'
                      }}
                      title={holiday.is_active ? 'Disable holiday' : 'Enable holiday'}
                    >
                      {holiday.is_active ? <CheckCircle size={14} /> : <XCircle size={14} />}
                      {holiday.is_active ? 'Active' : 'Inactive'}
                    </button>
                  ) : (
                    <span style={{
                      padding: '0.5rem',
                      backgroundColor: '#f9fafb',
                      color: '#6b7280',
                      borderRadius: '4px',
                      fontSize: '0.75rem',
                      fontStyle: 'italic'
                    }}>
                      Manual holiday
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default HolidayManagement;
