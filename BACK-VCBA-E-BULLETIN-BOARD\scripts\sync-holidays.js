#!/usr/bin/env node

/**
 * Holiday Sync Script
 * Synchronizes holidays from Nager.Date API to the database
 * Can be run manually or via cron job
 * 
 * Usage:
 *   node scripts/sync-holidays.js [year] [options]
 * 
 * Examples:
 *   node scripts/sync-holidays.js                    # Sync current year
 *   node scripts/sync-holidays.js 2025               # Sync specific year
 *   node scripts/sync-holidays.js 2025 --ph-only     # Philippine holidays only
 *   node scripts/sync-holidays.js 2025 --intl-only   # International holidays only
 *   node scripts/sync-holidays.js 2025 --no-clear    # Don't clear existing holidays
 */

const path = require('path');
const process = require('process');

// Add the src directory to the module path
require('module').globalPaths.push(path.join(__dirname, '..', 'src'));

const holidayService = require('../src/services/HolidayService');
const logger = require('../src/utils/logger');

/**
 * Parse command line arguments
 * @returns {Object} Parsed arguments
 */
function parseArguments() {
  const args = process.argv.slice(2);
  
  const options = {
    year: new Date().getFullYear(), // Default to current year
    includePhilippine: true,
    includeInternational: true,
    clearExisting: true,
    verbose: false,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--help' || arg === '-h') {
      options.help = true;
    } else if (arg === '--ph-only') {
      options.includePhilippine = true;
      options.includeInternational = false;
    } else if (arg === '--intl-only') {
      options.includePhilippine = false;
      options.includeInternational = true;
    } else if (arg === '--no-clear') {
      options.clearExisting = false;
    } else if (arg === '--verbose' || arg === '-v') {
      options.verbose = true;
    } else if (!isNaN(parseInt(arg))) {
      options.year = parseInt(arg);
    }
  }

  return options;
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Holiday Sync Script
==================

Synchronizes holidays from Nager.Date API to the database.

Usage:
  node scripts/sync-holidays.js [year] [options]

Arguments:
  year                Year to sync holidays for (default: current year)

Options:
  --ph-only          Sync Philippine holidays only
  --intl-only        Sync international holidays only
  --no-clear         Don't clear existing auto-generated holidays
  --verbose, -v      Enable verbose logging
  --help, -h         Show this help message

Examples:
  node scripts/sync-holidays.js                    # Sync current year
  node scripts/sync-holidays.js 2025               # Sync specific year
  node scripts/sync-holidays.js 2025 --ph-only     # Philippine holidays only
  node scripts/sync-holidays.js 2025 --intl-only   # International holidays only
  node scripts/sync-holidays.js 2025 --no-clear    # Don't clear existing holidays

Notes:
  - This script will automatically clear existing auto-generated holidays unless --no-clear is specified
  - Manual holidays (is_auto_generated = 0) are never affected
  - The script uses the Nager.Date API which is free and has no rate limits
  - Philippine holidays include local names and are marked as 'local' type
  - International holidays are those that appear in multiple countries
`);
}

/**
 * Validate year parameter
 * @param {number} year - Year to validate
 * @returns {boolean} True if valid
 */
function validateYear(year) {
  const currentYear = new Date().getFullYear();
  const minYear = currentYear - 5;  // Allow 5 years back
  const maxYear = currentYear + 10; // Allow 10 years forward
  
  if (year < minYear || year > maxYear) {
    console.error(`❌ Error: Year must be between ${minYear} and ${maxYear}`);
    return false;
  }
  
  return true;
}

/**
 * Format sync results for display
 * @param {Object} results - Sync results from holidayService
 */
function displayResults(results) {
  console.log('\n📊 Sync Results:');
  console.log('================');
  console.log(`Year: ${results.year}`);
  console.log(`Philippine Holidays: ${results.philippineHolidays}`);
  console.log(`International Holidays: ${results.internationalHolidays}`);
  console.log(`Total Inserted: ${results.totalInserted}`);
  
  if (results.clearedCount > 0) {
    console.log(`Existing Holidays Cleared: ${results.clearedCount}`);
  }
  
  if (results.errors && results.errors.length > 0) {
    console.log('\n⚠️  Errors:');
    results.errors.forEach(error => {
      console.log(`   - ${error}`);
    });
  }
  
  if (results.totalInserted > 0) {
    console.log(`\n✅ Successfully synced ${results.totalInserted} holidays for ${results.year}`);
  } else {
    console.log(`\n⚠️  No holidays were synced for ${results.year}`);
  }
}

/**
 * Main sync function
 */
async function main() {
  const options = parseArguments();
  
  if (options.help) {
    showHelp();
    process.exit(0);
  }
  
  if (!validateYear(options.year)) {
    process.exit(1);
  }
  
  console.log('🎄 Holiday Sync Script');
  console.log('======================');
  console.log(`Year: ${options.year}`);
  console.log(`Philippine Holidays: ${options.includePhilippine ? 'Yes' : 'No'}`);
  console.log(`International Holidays: ${options.includeInternational ? 'Yes' : 'No'}`);
  console.log(`Clear Existing: ${options.clearExisting ? 'Yes' : 'No'}`);
  console.log('');
  
  try {
    // Check current sync status
    console.log('📋 Checking current sync status...');
    const status = await holidayService.getHolidaySyncStatus(options.year);
    
    if (status.isSynced) {
      console.log(`📅 Found ${status.totalHolidays} existing holidays for ${options.year}`);
      console.log(`   - Philippine: ${status.philippineHolidays}`);
      console.log(`   - International: ${status.internationalHolidays}`);
      console.log(`   - School: ${status.schoolHolidays}`);
      console.log(`   - Last sync: ${status.lastSync}`);
      
      if (!options.clearExisting) {
        console.log('\n⚠️  Existing holidays found and --no-clear specified.');
        console.log('   This may result in duplicate holidays.');
        console.log('   Consider using --clear or removing existing holidays first.');
      }
    } else {
      console.log(`📅 No existing holidays found for ${options.year}`);
    }
    
    // Perform sync
    console.log('\n🔄 Starting holiday sync...');
    const results = await holidayService.syncHolidays(options.year, {
      includePhilippine: options.includePhilippine,
      includeInternational: options.includeInternational,
      clearExisting: options.clearExisting
    });
    
    // Display results
    displayResults(results);
    
    // Exit with appropriate code
    if (results.errors && results.errors.length > 0) {
      process.exit(1); // Partial failure
    } else {
      process.exit(0); // Success
    }
    
  } catch (error) {
    console.error('\n❌ Holiday sync failed:', error.message);
    logger.error('Holiday sync script failed:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  logger.error('Unhandled Rejection in holiday sync script:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  logger.error('Uncaught Exception in holiday sync script:', error);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, parseArguments, validateYear };
