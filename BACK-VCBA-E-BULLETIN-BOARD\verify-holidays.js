const mysql = require('mysql2/promise');

async function verifyHolidays() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'db_ebulletin_system'
  });

  try {
    console.log('=== HOLIDAY SYNC VERIFICATION ===');
    
    const [holidays] = await connection.execute(`
      SELECT 
        title, 
        event_date, 
        holiday_type, 
        country_code, 
        local_name,
        is_auto_generated,
        api_source
      FROM school_calendar 
      WHERE is_holiday = 1 
      AND is_auto_generated = 1 
      AND YEAR(event_date) = 2025
      ORDER BY event_date
    `);
    
    console.log(`Found ${holidays.length} auto-generated holidays for 2025:`);
    console.table(holidays);
    
    // Count by type
    const [counts] = await connection.execute(`
      SELECT 
        holiday_type,
        COUNT(*) as count
      FROM school_calendar 
      WHERE is_holiday = 1 
      AND is_auto_generated = 1 
      AND YEAR(event_date) = 2025
      GROUP BY holiday_type
    `);
    
    console.log('\nHoliday counts by type:');
    console.table(counts);
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

verifyHolidays();
