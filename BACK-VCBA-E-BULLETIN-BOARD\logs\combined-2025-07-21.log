{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:28',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:28',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:28'
}
{
  message: '::1 - - [20/Jul/2025:23:29:28 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:28'
}
{
  message: '::1 - - [20/Jul/2025:23:29:28 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:28'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:28',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:29'
}
{
  message: '::1 - - [20/Jul/2025:23:29:29 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:29'
}
{
  message: '::1 - - [20/Jul/2025:23:29:29 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:29'
}
{
  message: '::1 - - [20/Jul/2025:23:29:29 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:29'
}
{
  message: '::1 - - [20/Jul/2025:23:29:29 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:29:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:29:59'
}
{
  message: '::1 - - [20/Jul/2025:23:29:59 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:59'
}
{
  message: '::1 - - [20/Jul/2025:23:29:59 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:29:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:30:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:29'
}
{
  message: '::1 - - [20/Jul/2025:23:30:29 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:30:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:30:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:29'
}
{
  message: '::1 - - [20/Jul/2025:23:30:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:30:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:30:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:30:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:30:59'
}
{
  message: '::1 - - [20/Jul/2025:23:30:59 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:30:59'
}
{
  message: '::1 - - [20/Jul/2025:23:30:59 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:30:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:31:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:31:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:29'
}
{
  message: '::1 - - [20/Jul/2025:23:31:29 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:31:29'
}
{
  message: '::1 - - [20/Jul/2025:23:31:29 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:31:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:31:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:31:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:31:59'
}
{
  message: '::1 - - [20/Jul/2025:23:31:59 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:31:59'
}
{
  message: '::1 - - [20/Jul/2025:23:31:59 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:31:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:32:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:32:29',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:29'
}
{
  message: '::1 - - [20/Jul/2025:23:32:29 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:32:29'
}
{
  message: '::1 - - [20/Jul/2025:23:32:29 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:32:29'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:32:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:32:59',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:32:59'
}
{
  message: '::1 - - [20/Jul/2025:23:32:59 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:32:59'
}
{
  message: '::1 - - [20/Jul/2025:23:32:59 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:32:59'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:33:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:33:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:33:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:33:46'
}
{
  message: '::1 - - [20/Jul/2025:23:33:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:33:46'
}
{
  message: '::1 - - [20/Jul/2025:23:33:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:33:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:34:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:34:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:34:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:34:46'
}
{
  message: '::1 - - [20/Jul/2025:23:34:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:34:46'
}
{
  message: '::1 - - [20/Jul/2025:23:34:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:34:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:35:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:35:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:35:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:35:46'
}
{
  message: '::1 - - [20/Jul/2025:23:35:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:46'
}
{
  message: '::1 - - [20/Jul/2025:23:35:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:46'
}
{
  message: '[HolidayService] Fetching holidays for PH 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:52'
}
{
  message: '[HolidayService] Successfully fetched 21 holidays for PH 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:52'
}
{
  message: '[HolidayService] Fetching holidays for US 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:52'
}
{
  message: '[HolidayService] Successfully fetched 16 holidays for US 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:52'
}
{
  message: '[HolidayService] Using cached holidays for PH 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:53'
}
{
  message: '[HolidayService] Using cached holidays for US 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:35:53'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:36:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:36:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:36:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:36:46'
}
{
  message: '::1 - - [20/Jul/2025:23:36:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:36:46'
}
{
  message: '::1 - - [20/Jul/2025:23:36:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:36:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:37:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:37:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:37:46',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:37:46'
}
{
  message: '::1 - - [20/Jul/2025:23:37:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:37:46'
}
{
  message: '::1 - - [20/Jul/2025:23:37:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:37:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:38:38',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:38:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:38:38',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:38:38'
}
{
  message: '::1 - - [20/Jul/2025:23:38:38 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:38:38'
}
{
  message: '::1 - - [20/Jul/2025:23:38:38 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:38:38'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:21',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:21',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:21'
}
{
  message: '::1 - - [20/Jul/2025:23:39:21 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:21'
}
{
  message: '::1 - - [20/Jul/2025:23:39:21 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:24 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:24'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  message: '::1 - - [20/Jul/2025:23:39:27 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:27'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:37',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/calendar/71/attachments',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:37'
}
{
  message: '::1 - - [20/Jul/2025:23:39:37 +0000] "GET /api/calendar/71/attachments HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:37'
}
{
  message: '::1 - - [20/Jul/2025:23:39:50 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:50'
}
{
  message: '::1 - - [20/Jul/2025:23:39:50 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:50'
}
{
  message: '::1 - - [20/Jul/2025:23:39:50 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:50'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:54',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:39:54',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:39:54'
}
{
  message: '::1 - - [20/Jul/2025:23:39:54 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:54'
}
{
  message: '::1 - - [20/Jul/2025:23:39:54 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:39:54'
}
{
  message: '::1 - - [20/Jul/2025:23:40:14 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:14'
}
{
  message: '::1 - - [20/Jul/2025:23:40:14 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:14'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:40:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:40:24',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:24'
}
{
  message: '::1 - - [20/Jul/2025:23:40:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:24'
}
{
  message: '::1 - - [20/Jul/2025:23:40:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:24'
}
{
  message: '::1 - - [20/Jul/2025:23:40:30 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:30'
}
{
  message: '::1 - - [20/Jul/2025:23:40:30 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:30'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:40:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:40:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:40:55'
}
{
  message: '::1 - - [20/Jul/2025:23:40:55 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:55'
}
{
  message: '::1 - - [20/Jul/2025:23:40:55 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:55'
}
{
  message: '::1 - - [20/Jul/2025:23:40:56 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:56'
}
{
  message: '::1 - - [20/Jul/2025:23:40:56 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:40:56'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:41:25',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:41:25',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:25'
}
{
  message: '::1 - - [20/Jul/2025:23:41:25 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:41:25'
}
{
  message: '::1 - - [20/Jul/2025:23:41:25 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:41:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:41:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:41:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:41:55'
}
{
  message: '::1 - - [20/Jul/2025:23:41:55 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:41:55'
}
{
  message: '::1 - - [20/Jul/2025:23:41:55 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:41:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:42:25',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:42:25',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:25'
}
{
  message: '::1 - - [20/Jul/2025:23:42:25 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:42:25'
}
{
  message: '::1 - - [20/Jul/2025:23:42:25 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:42:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:42:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications?limit=10&sort_by=created_at&sort_order=DESC',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verification_failed',
  userId: null,
  success: false,
  timestamp: '2025-07-21 07:42:55',
  error: 'Access token expired',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Access token expired',
  stack: 'Error: Access token expired\n' +
    '    at JWTUtil.verifyAccessToken (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\utils\\jwt.js:57:15)\n' +
    '    at authenticate (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\auth.js:19:29)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at Function.handle (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:175:3)\n' +
    '    at router (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:47:12)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/notifications/unread-count',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 07:42:55'
}
{
  message: '::1 - - [20/Jul/2025:23:42:55 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:42:55'
}
{
  message: '::1 - - [20/Jul/2025:23:42:55 +0000] "GET /api/notifications/unread-count HTTP/1.1" 401 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:42:55'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'admin_login_success',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:08',
  email: '<EMAIL>',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "POST /api/auth/login HTTP/1.1" 200 1017 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:08',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:08',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:08',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:08',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  message: '::1 - - [20/Jul/2025:23:43:08 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:08'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  message: '::1 - - [20/Jul/2025:23:43:10 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:10'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:20',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:21',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:21',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:21',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  message: '::1 - - [20/Jul/2025:23:43:21 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:21'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:32 +0000] "GET /api/calendar/71/attachments HTTP/1.1" 200 95 "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:47',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:47',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:47',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:43:47',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  message: '::1 - - [20/Jul/2025:23:43:47 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:43:47'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:44:18',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 07:44:18',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [20/Jul/2025:23:44:18 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:44:18'
}
{
  message: '::1 - - [20/Jul/2025:23:44:18 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:44:18'
}
{
  message: '[HolidayService] Fetching holidays for PH 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:37'
}
{
  message: '[HolidayService] Successfully fetched 21 holidays for PH 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:39'
}
{
  message: '[HolidayService] Fetching holidays for PH 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:40'
}
{
  message: '[HolidayService] Successfully fetched 18 holidays for PH 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:41'
}
{
  message: '[HolidayService] Fetching holidays for US 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:41'
}
{
  message: '[HolidayService] Successfully fetched 16 holidays for US 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:41'
}
{
  message: '[HolidayService] Fetching holidays for US 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:41'
}
{
  message: '[HolidayService] Successfully fetched 16 holidays for US 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:51:43'
}
{
  message: '[HolidayService] Fetching holidays for PH 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:55'
}
{
  message: '[HolidayService] Successfully fetched 21 holidays, filtered to 21 global/Philippine holidays for PH 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:55'
}
{
  message: '[HolidayService] Fetching holidays for PH 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:55'
}
{
  message: '[HolidayService] Successfully fetched 18 holidays, filtered to 18 global/Philippine holidays for PH 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:56'
}
{
  message: '[HolidayService] Fetching holidays for US 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:57'
}
{
  message: '[HolidayService] Successfully fetched 16 holidays, filtered to 4 global/Philippine holidays for US 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:57'
}
{
  message: '[HolidayService] Fetching holidays for US 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:57'
}
{
  message: '[HolidayService] Successfully fetched 16 holidays, filtered to 4 global/Philippine holidays for US 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:57'
}
{
  message: '[HolidayService] Fetching holidays for GB 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:58'
}
{
  message: '[HolidayService] Successfully fetched 13 holidays, filtered to 4 global/Philippine holidays for GB 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:59'
}
{
  message: '[HolidayService] Fetching holidays for GB 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 07:59:59'
}
{
  message: '[HolidayService] Successfully fetched 13 holidays, filtered to 4 global/Philippine holidays for GB 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:00:01'
}
{
  message: '[HolidayService] Fetching holidays for CA 2025 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:00:02'
}
{
  message: '[HolidayService] Successfully fetched 31 holidays, filtered to 4 global/Philippine holidays for CA 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:00:03'
}
{
  message: '[HolidayService] Fetching holidays for CA 2026 from API',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:00:03'
}
{
  message: '[HolidayService] Successfully fetched 31 holidays, filtered to 4 global/Philippine holidays for CA 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:00:04'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:10',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  message: '::1 - - [21/Jul/2025:00:10:10 +0000] "GET /api/admin/students?page=1&limit=10&grade_level=12&is_active=true HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:10'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  message: '::1 - - [21/Jul/2025:00:10:20 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:20'
}
{
  message: '::1 - - [21/Jul/2025:00:10:20 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:20'
}
{
  message: '::1 - - [21/Jul/2025:00:10:20 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:20'
}
{
  message: '::1 - - [21/Jul/2025:00:10:20 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:20'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/sync-multiple not found',
  stack: 'Error: Route /api/holidays/sync-multiple not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'POST',
    url: '/api/holidays/sync-multiple',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:23'
}
{
  message: '::1 - - [21/Jul/2025:00:10:23 +0000] "POST /api/holidays/sync-multiple HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:40',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:40',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:40 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:40'
}
{
  message: '::1 - - [21/Jul/2025:00:10:40 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:40'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:57',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:57',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:57',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:10:57',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  message: '::1 - - [21/Jul/2025:00:10:57 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:10:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:24'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  message: '::1 - - [21/Jul/2025:00:11:33 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:33'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  message: '::1 - - [21/Jul/2025:00:11:43 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:43'
}
{
  message: '::1 - - [21/Jul/2025:00:11:43 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:11:43'
}
{
  message: '::1 - - [21/Jul/2025:00:11:43 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:43'
}
{
  message: '::1 - - [21/Jul/2025:00:11:43 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:43'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:54',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:11:54',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:11:54 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:54'
}
{
  message: '::1 - - [21/Jul/2025:00:11:54 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:11:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:12:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:12:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:12:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:12:24'
}
{
  message: '::1 - - [21/Jul/2025:00:12:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:12:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:12:54',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:12:54',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:12:54 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:12:54'
}
{
  message: '::1 - - [21/Jul/2025:00:12:54 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:12:54'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:24',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:13:24 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:24'
}
{
  message: '::1 - - [21/Jul/2025:00:13:24 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:24'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:13:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:35 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:35'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  message: '::1 - - [21/Jul/2025:00:13:37 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:37'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  message: '::1 - - [21/Jul/2025:00:13:57 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:57'
}
{
  message: '::1 - - [21/Jul/2025:00:13:57 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays?year=2025 not found',
  stack: 'Error: Route /api/holidays?year=2025 not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays?year=2025',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  message: 'Application Error Route /api/holidays/stats not found',
  stack: 'Error: Route /api/holidays/stats not found\n' +
    '    at notFoundHandler (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\src\\middleware\\notFoundHandler.js:5:8)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at trim_prefix (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:328:13)\n' +
    '    at D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:286:9\n' +
    '    at Function.process_params (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:346:12)\n' +
    '    at next (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\index.js:280:10)\n' +
    '    at logger (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\morgan\\index.js:144:5)\n' +
    '    at newFn (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express-async-errors\\index.js:16:20)\n' +
    '    at Layer.handle [as handle_request] (D:\\online-e-bulletin-reactjs\\BACK-VCBA-E-BULLETIN-BOARD\\node_modules\\express\\lib\\router\\layer.js:95:5)',
  name: 'Error',
  request: {
    method: 'GET',
    url: '/api/holidays/stats',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    ip: '::1',
    userId: undefined,
    userRole: undefined
  },
  level: 'error',
  timestamp: '2025-07-21 08:13:57'
}
{
  message: '::1 - - [21/Jul/2025:00:13:57 +0000] "GET /api/holidays?year=2025 HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:57'
}
{
  message: '::1 - - [21/Jul/2025:00:13:57 +0000] "GET /api/holidays/stats HTTP/1.1" 404 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:13:57'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:14:05',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:14:05',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:14:05 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:14:05'
}
{
  message: '::1 - - [21/Jul/2025:00:14:05 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:14:05'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:14:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:14:35',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:14:35 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:14:35'
}
{
  message: '::1 - - [21/Jul/2025:00:14:35 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:14:35'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:05',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:05',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:15:05 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:05'
}
{
  message: '::1 - - [21/Jul/2025:00:15:05 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:05'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  message: '::1 - - [21/Jul/2025:00:15:23 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:15:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:15:53 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:53'
}
{
  message: '::1 - - [21/Jul/2025:00:15:53 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:15:53'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:16:25',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:16:25 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:16:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:16:25',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:16:25 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:16:25'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:16:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:16:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:16:53 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:16:53'
}
{
  message: '::1 - - [21/Jul/2025:00:16:53 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:16:53'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:17:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:17:23',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:17:23 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:17:23'
}
{
  message: '::1 - - [21/Jul/2025:00:17:23 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:17:23'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:17:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:17:53',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:17:53 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:17:53'
}
{
  message: '::1 - - [21/Jul/2025:00:17:53 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:17:53'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  message: '::1 - - [21/Jul/2025:00:18:01 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:01'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:18:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:18:31 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:31'
}
{
  message: '::1 - - [21/Jul/2025:00:18:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:18:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:19:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:19:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:19:01 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:19:01'
}
{
  message: '::1 - - [21/Jul/2025:00:19:01 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:19:01'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:19:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:19:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:19:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:19:31'
}
{
  message: '::1 - - [21/Jul/2025:00:19:31 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:19:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:01',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:20:01 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:01'
}
{
  message: '::1 - - [21/Jul/2025:00:20:01 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:01'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:20:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:31'
}
{
  message: '::1 - - [21/Jul/2025:00:20:31 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:57',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:58',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:58',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:20:58',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  message: '::1 - - [21/Jul/2025:00:20:58 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:20:58'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  message: '::1 - - [21/Jul/2025:00:21:14 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:14'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:21:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:21:45 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:45'
}
{
  message: '::1 - - [21/Jul/2025:00:21:45 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:21:45'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:22:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:22:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:22:15 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:22:15'
}
{
  message: '::1 - - [21/Jul/2025:00:22:15 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:22:15'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:22:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:22:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:22:45 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:22:45'
}
{
  message: '::1 - - [21/Jul/2025:00:22:45 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:22:45'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:23:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:23:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:23:15 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:23:15'
}
{
  message: '::1 - - [21/Jul/2025:00:23:15 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:23:15'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:23:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:23:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:23:45 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:23:45'
}
{
  message: '::1 - - [21/Jul/2025:00:23:45 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:23:45'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:24:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:24:15',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:24:15 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:24:15'
}
{
  message: '::1 - - [21/Jul/2025:00:24:15 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:24:15'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:24:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:24:45',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:24:45 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:24:45'
}
{
  message: '::1 - - [21/Jul/2025:00:24:45 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:24:45'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:25:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:25:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:25:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:25:46'
}
{
  message: '::1 - - [21/Jul/2025:00:25:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:25:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:26:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:26:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:26:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:26:46'
}
{
  message: '::1 - - [21/Jul/2025:00:26:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:26:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:27:27',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:27:27',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:27:27 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:27:27'
}
{
  message: '::1 - - [21/Jul/2025:00:27:27 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:27:27'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:27:44',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:27:44',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:27:44 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:27:44'
}
{
  message: '::1 - - [21/Jul/2025:00:27:44 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:27:44'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:28:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:28:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:28:14 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:28:14'
}
{
  message: '::1 - - [21/Jul/2025:00:28:14 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:28:14'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:28:44',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:28:44',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:28:44 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:28:44'
}
{
  message: '::1 - - [21/Jul/2025:00:28:44 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:28:44'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:14',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:29:14 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:14'
}
{
  message: '::1 - - [21/Jul/2025:00:29:14 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:14'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:29:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:31',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:29:31 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:31'
}
{
  message: '::1 - - [21/Jul/2025:00:29:31 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:31'
}
{
  message: '::1 - - [21/Jul/2025:00:29:31 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:31'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:29:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 200 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/calendar/view?year=2025&month=7 HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/announcements/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  message: '::1 - - [21/Jul/2025:00:29:32 +0000] "GET /api/calendar/categories/with-subcategories HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:29:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:30:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:30:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:30:02 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:30:02'
}
{
  message: '::1 - - [21/Jul/2025:00:30:02 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:30:02'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:30:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:30:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:30:32 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:30:32'
}
{
  message: '::1 - - [21/Jul/2025:00:30:32 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:30:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:31:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:31:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:31:02 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:31:02'
}
{
  message: '::1 - - [21/Jul/2025:00:31:02 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:31:02'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:31:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:31:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:31:32 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:31:32'
}
{
  message: '::1 - - [21/Jul/2025:00:31:32 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:31:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:32:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:32:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:32:02 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:32:02'
}
{
  message: '::1 - - [21/Jul/2025:00:32:02 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:32:02'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:32:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:32:32',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:32:32 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:32:32'
}
{
  message: '::1 - - [21/Jul/2025:00:32:32 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:32:32'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:33:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:33:02',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:33:02 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:33:02'
}
{
  message: '::1 - - [21/Jul/2025:00:33:02 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:33:02'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:33:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:33:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:33:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:33:46'
}
{
  message: '::1 - - [21/Jul/2025:00:33:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:33:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:34:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:34:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:34:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:34:46'
}
{
  message: '::1 - - [21/Jul/2025:00:34:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:34:46'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:35:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  action: 'token_verified',
  userId: 31,
  success: true,
  timestamp: '2025-07-21 08:35:46',
  role: 'admin',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
  level: 'info',
  message: 'Authentication Event'
}
{
  message: '::1 - - [21/Jul/2025:00:35:46 +0000] "GET /api/notifications/unread-count HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:35:46'
}
{
  message: '::1 - - [21/Jul/2025:00:35:46 +0000] "GET /api/notifications?limit=10&sort_by=created_at&sort_order=DESC HTTP/1.1" 304 - "http://localhost:3000/" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:35:46'
}
{
  message: 'Syncing holidays for current year (2025) and next year (2026)',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:01'
}
{
  message: 'Starting holiday sync for year 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:01'
}
{
  message: 'Getting filtered holidays for year 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:03'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2025/PH',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:03'
}
{
  message: 'Successfully fetched 21 holidays for PH 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:04'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2025/US',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:04'
}
{
  message: 'Successfully fetched 16 holidays for US 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:05'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2025/GB',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:05'
}
{
  message: 'Successfully fetched 13 holidays for GB 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Filtered holidays: 21 Philippine, 4 global',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Prepared 25 holidays for database sync',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Removed 0 existing auto-generated holidays for 2025',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Holiday sync completed for 2025: 22 added, 3 skipped',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Starting holiday sync for year 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Getting filtered holidays for year 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2026/PH',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:06'
}
{
  message: 'Successfully fetched 18 holidays for PH 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:07'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2026/US',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:07'
}
{
  message: 'Successfully fetched 16 holidays for US 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:08'
}
{
  message: 'Fetching holidays from: https://date.nager.at/api/v3/publicholidays/2026/GB',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:08'
}
{
  message: 'Successfully fetched 13 holidays for GB 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:09'
}
{
  message: 'Filtered holidays: 18 Philippine, 4 global',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:09'
}
{
  message: 'Prepared 22 holidays for database sync',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:09'
}
{
  message: 'Removed 0 existing auto-generated holidays for 2026',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:09'
}
{
  message: 'Holiday sync completed for 2026: 19 added, 3 skipped',
  level: 'info',
  service: 'VCBA E-Bulletin Board System',
  version: '1.0.0',
  timestamp: '2025-07-21 08:36:09'
}
