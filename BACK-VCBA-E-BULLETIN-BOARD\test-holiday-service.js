const path = require('path');

// Add the src directory to the module path
require('module').globalPaths.push(path.join(__dirname, 'src'));

const holidayService = require('./src/services/HolidayService');

async function testHolidayService() {
  console.log('🧪 Testing Holiday Service');
  console.log('==========================');
  
  try {
    // Test 1: Fetch Philippine holidays
    console.log('\n1️⃣ Testing Philippine holidays fetch...');
    const phHolidays = await holidayService.fetchPhilippineHolidays(2025);
    console.log(`✅ Fetched ${phHolidays.length} Philippine holidays`);
    console.log('Sample Philippine holidays:');
    phHolidays.slice(0, 3).forEach(holiday => {
      console.log(`   - ${holiday.title} (${holiday.event_date})`);
    });
    
    // Test 2: Fetch international holidays
    console.log('\n2️⃣ Testing international holidays fetch...');
    const intlHolidays = await holidayService.fetchInternationalHolidays(2025);
    console.log(`✅ Fetched ${intlHolidays.length} international holidays`);
    console.log('Sample international holidays:');
    intlHolidays.slice(0, 3).forEach(holiday => {
      console.log(`   - ${holiday.title} (${holiday.event_date})`);
    });
    
    // Test 3: Check sync status
    console.log('\n3️⃣ Testing sync status check...');
    const status = await holidayService.getHolidaySyncStatus(2025);
    console.log('✅ Sync status:', status);
    
    console.log('\n🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error);
  }
}

testHolidayService();
