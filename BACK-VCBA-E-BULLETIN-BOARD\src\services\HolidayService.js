const https = require('https');
const database = require('../config/database');
const logger = require('../utils/logger');

/**
 * Holiday Service
 * Manages automatic holiday fetching and synchronization from Nager.Date API
 * Handles both Philippine holidays and internationally recognized holidays
 */
class HolidayService {
  // Holiday types mapping
  static HOLIDAY_TYPES = {
    LOCAL: 'local',           // Philippines-specific holidays
    INTERNATIONAL: 'international', // Globally recognized holidays
    SCHOOL: 'school'          // School-specific holidays (manual)
  };

  // API configuration
  static API_CONFIG = {
    BASE_URL: 'https://date.nager.at/api/v3',
    TIMEOUT: 10000, // 10 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
  };

  // Countries to fetch for international holidays
  static INTERNATIONAL_COUNTRIES = ['US', 'GB', 'CA', 'AU'];
  
  // Philippines country code
  static PHILIPPINES_CODE = 'PH';

  // Category and subcategory for holidays
  static HOLIDAY_CATEGORY = {
    CATEGORY_ID: 1,      // General category
    SUBCATEGORY_ID: 13   // Holidays subcategory
  };

  /**
   * Fetch holidays from Nager.Date API
   * @param {string} countryCode - ISO country code (e.g., 'PH', 'US')
   * @param {number} year - Year to fetch holidays for
   * @returns {Promise<Array>} Array of holiday objects
   */
  async fetchHolidaysFromAPI(countryCode, year) {
    return new Promise((resolve, reject) => {
      const url = `${HolidayService.API_CONFIG.BASE_URL}/publicholidays/${year}/${countryCode}`;
      
      logger.info(`Fetching holidays from API: ${url}`);
      
      const request = https.get(url, { timeout: HolidayService.API_CONFIG.TIMEOUT }, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            if (res.statusCode !== 200) {
              throw new Error(`API returned status ${res.statusCode}: ${data}`);
            }
            
            const holidays = JSON.parse(data);
            logger.info(`Successfully fetched ${holidays.length} holidays for ${countryCode} ${year}`);
            resolve(holidays);
          } catch (error) {
            logger.error(`Error parsing API response for ${countryCode} ${year}:`, error);
            reject(error);
          }
        });
      });
      
      request.on('error', (error) => {
        logger.error(`API request failed for ${countryCode} ${year}:`, error);
        reject(error);
      });
      
      request.on('timeout', () => {
        request.destroy();
        const error = new Error(`API request timeout for ${countryCode} ${year}`);
        logger.error(error.message);
        reject(error);
      });
    });
  }

  /**
   * Fetch holidays with retry mechanism
   * @param {string} countryCode - ISO country code
   * @param {number} year - Year to fetch holidays for
   * @param {number} attempt - Current attempt number
   * @returns {Promise<Array>} Array of holiday objects
   */
  async fetchHolidaysWithRetry(countryCode, year, attempt = 1) {
    try {
      return await this.fetchHolidaysFromAPI(countryCode, year);
    } catch (error) {
      if (attempt < HolidayService.API_CONFIG.RETRY_ATTEMPTS) {
        logger.warn(`Retry attempt ${attempt} for ${countryCode} ${year} after error:`, error.message);
        await this.delay(HolidayService.API_CONFIG.RETRY_DELAY * attempt);
        return this.fetchHolidaysWithRetry(countryCode, year, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * Delay utility for retry mechanism
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Fetch Philippine holidays
   * @param {number} year - Year to fetch holidays for
   * @returns {Promise<Array>} Array of Philippine holiday objects
   */
  async fetchPhilippineHolidays(year) {
    try {
      const holidays = await this.fetchHolidaysWithRetry(HolidayService.PHILIPPINES_CODE, year);
      
      // Transform API data to our database format
      return holidays.map(holiday => ({
        title: holiday.name,
        description: `${holiday.localName} - Philippine Holiday`,
        event_date: holiday.date,
        end_date: holiday.date,
        category_id: HolidayService.HOLIDAY_CATEGORY.CATEGORY_ID,
        subcategory_id: HolidayService.HOLIDAY_CATEGORY.SUBCATEGORY_ID,
        is_recurring: 0,
        recurrence_pattern: null,
        is_active: 1,
        is_published: 1,
        allow_comments: 1,
        is_alert: 0,
        is_holiday: 1,
        holiday_type: HolidayService.HOLIDAY_TYPES.LOCAL,
        country_code: HolidayService.PHILIPPINES_CODE,
        is_auto_generated: 1,
        api_source: 'nager.date',
        local_name: holiday.localName,
        holiday_types: JSON.stringify(holiday.types || []),
        is_global: holiday.global || false,
        is_fixed: holiday.fixed || false,
        created_by: 1 // System user
      }));
    } catch (error) {
      logger.error(`Failed to fetch Philippine holidays for ${year}:`, error);
      throw error;
    }
  }

  /**
   * Fetch international holidays (common across multiple countries)
   * @param {number} year - Year to fetch holidays for
   * @returns {Promise<Array>} Array of international holiday objects
   */
  async fetchInternationalHolidays(year) {
    try {
      const allCountryHolidays = {};
      
      // Fetch holidays from multiple countries
      for (const countryCode of HolidayService.INTERNATIONAL_COUNTRIES) {
        try {
          const holidays = await this.fetchHolidaysWithRetry(countryCode, year);
          allCountryHolidays[countryCode] = holidays;
        } catch (error) {
          logger.warn(`Failed to fetch holidays for ${countryCode}:`, error.message);
          // Continue with other countries
        }
      }
      
      // Find holidays that appear in multiple countries (international)
      const holidayNames = new Map();
      
      Object.entries(allCountryHolidays).forEach(([country, holidays]) => {
        holidays.forEach(holiday => {
          const name = holiday.name;
          if (!holidayNames.has(name)) {
            holidayNames.set(name, {
              countries: [],
              dates: [],
              examples: []
            });
          }
          holidayNames.get(name).countries.push(country);
          holidayNames.get(name).dates.push(holiday.date);
          holidayNames.get(name).examples.push({
            country,
            date: holiday.date,
            localName: holiday.localName,
            types: holiday.types,
            fixed: holiday.fixed,
            global: holiday.global
          });
        });
      });
      
      // Filter for holidays that appear in at least 2 countries
      const internationalHolidays = [];
      
      holidayNames.forEach((data, name) => {
        if (data.countries.length >= 2) {
          // Use the most common date (first occurrence)
          const primaryExample = data.examples[0];
          
          internationalHolidays.push({
            title: name,
            description: `International Holiday - Celebrated in ${data.countries.join(', ')}`,
            event_date: primaryExample.date,
            end_date: primaryExample.date,
            category_id: HolidayService.HOLIDAY_CATEGORY.CATEGORY_ID,
            subcategory_id: HolidayService.HOLIDAY_CATEGORY.SUBCATEGORY_ID,
            is_recurring: 0,
            recurrence_pattern: null,
            is_active: 1,
            is_published: 1,
            allow_comments: 1,
            is_alert: 0,
            is_holiday: 1,
            holiday_type: HolidayService.HOLIDAY_TYPES.INTERNATIONAL,
            country_code: null, // International, no specific country
            is_auto_generated: 1,
            api_source: 'nager.date',
            local_name: name,
            holiday_types: JSON.stringify(primaryExample.types || []),
            is_global: primaryExample.global || false,
            is_fixed: primaryExample.fixed || false,
            created_by: 1 // System user
          });
        }
      });
      
      logger.info(`Found ${internationalHolidays.length} international holidays for ${year}`);
      return internationalHolidays;
    } catch (error) {
      logger.error(`Failed to fetch international holidays for ${year}:`, error);
      throw error;
    }
  }

  /**
   * Get existing holidays from database for a specific year
   * @param {number} year - Year to check
   * @returns {Promise<Array>} Array of existing holiday records
   */
  async getExistingHolidays(year) {
    try {
      const sql = `
        SELECT * FROM school_calendar
        WHERE is_auto_generated = 1
        AND is_holiday = 1
        AND YEAR(event_date) = ?
        AND deleted_at IS NULL
      `;

      const holidays = await database.query(sql, [year]);
      logger.info(`Found ${holidays.length} existing auto-generated holidays for ${year}`);
      return holidays;
    } catch (error) {
      logger.error(`Failed to get existing holidays for ${year}:`, error);
      throw error;
    }
  }

  /**
   * Clear existing auto-generated holidays for a specific year
   * @param {number} year - Year to clear holidays for
   * @returns {Promise<number>} Number of holidays cleared
   */
  async clearExistingHolidays(year) {
    try {
      const sql = `
        UPDATE school_calendar
        SET deleted_at = NOW()
        WHERE is_auto_generated = 1
        AND is_holiday = 1
        AND YEAR(event_date) = ?
        AND deleted_at IS NULL
      `;

      const result = await database.query(sql, [year]);
      const clearedCount = result.affectedRows || 0;
      logger.info(`Cleared ${clearedCount} existing auto-generated holidays for ${year}`);
      return clearedCount;
    } catch (error) {
      logger.error(`Failed to clear existing holidays for ${year}:`, error);
      throw error;
    }
  }

  /**
   * Insert holidays into database
   * @param {Array} holidays - Array of holiday objects to insert
   * @returns {Promise<Array>} Array of inserted holiday IDs
   */
  async insertHolidays(holidays) {
    if (!holidays || holidays.length === 0) {
      logger.info('No holidays to insert');
      return [];
    }

    try {
      const insertedIds = [];

      for (const holiday of holidays) {
        try {
          const result = await database.insert('school_calendar', holiday);
          insertedIds.push(result.insertId);
          logger.debug(`Inserted holiday: ${holiday.title} (ID: ${result.insertId})`);
        } catch (error) {
          logger.error(`Failed to insert holiday ${holiday.title}:`, error);
          // Continue with other holidays
        }
      }

      logger.info(`Successfully inserted ${insertedIds.length} out of ${holidays.length} holidays`);
      return insertedIds;
    } catch (error) {
      logger.error('Failed to insert holidays:', error);
      throw error;
    }
  }

  /**
   * Sync holidays for a specific year
   * @param {number} year - Year to sync holidays for
   * @param {Object} options - Sync options
   * @param {boolean} options.includePhilippine - Include Philippine holidays
   * @param {boolean} options.includeInternational - Include international holidays
   * @param {boolean} options.clearExisting - Clear existing holidays before sync
   * @returns {Promise<Object>} Sync results
   */
  async syncHolidays(year, options = {}) {
    const {
      includePhilippine = true,
      includeInternational = true,
      clearExisting = true
    } = options;

    logger.info(`Starting holiday sync for ${year}`, {
      includePhilippine,
      includeInternational,
      clearExisting
    });

    const results = {
      year,
      philippineHolidays: 0,
      internationalHolidays: 0,
      totalInserted: 0,
      clearedCount: 0,
      errors: []
    };

    try {
      // Clear existing holidays if requested
      if (clearExisting) {
        results.clearedCount = await this.clearExistingHolidays(year);
      }

      const allHolidays = [];

      // Fetch Philippine holidays
      if (includePhilippine) {
        try {
          const philippineHolidays = await this.fetchPhilippineHolidays(year);
          allHolidays.push(...philippineHolidays);
          results.philippineHolidays = philippineHolidays.length;
          logger.info(`Fetched ${philippineHolidays.length} Philippine holidays`);
        } catch (error) {
          const errorMsg = `Failed to fetch Philippine holidays: ${error.message}`;
          logger.error(errorMsg);
          results.errors.push(errorMsg);
        }
      }

      // Fetch international holidays
      if (includeInternational) {
        try {
          const internationalHolidays = await this.fetchInternationalHolidays(year);
          allHolidays.push(...internationalHolidays);
          results.internationalHolidays = internationalHolidays.length;
          logger.info(`Fetched ${internationalHolidays.length} international holidays`);
        } catch (error) {
          const errorMsg = `Failed to fetch international holidays: ${error.message}`;
          logger.error(errorMsg);
          results.errors.push(errorMsg);
        }
      }

      // Insert all holidays
      if (allHolidays.length > 0) {
        const insertedIds = await this.insertHolidays(allHolidays);
        results.totalInserted = insertedIds.length;
      }

      logger.info(`Holiday sync completed for ${year}`, results);
      return results;
    } catch (error) {
      logger.error(`Holiday sync failed for ${year}:`, error);
      results.errors.push(`Sync failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get holiday sync status for a year
   * @param {number} year - Year to check
   * @returns {Promise<Object>} Sync status information
   */
  async getHolidaySyncStatus(year) {
    try {
      const sql = `
        SELECT
          holiday_type,
          COUNT(*) as count,
          MIN(created_at) as first_sync,
          MAX(updated_at) as last_update
        FROM school_calendar
        WHERE is_auto_generated = 1
        AND is_holiday = 1
        AND YEAR(event_date) = ?
        AND deleted_at IS NULL
        GROUP BY holiday_type
      `;

      const stats = await database.query(sql, [year]);

      const status = {
        year,
        lastSync: null,
        totalHolidays: 0,
        philippineHolidays: 0,
        internationalHolidays: 0,
        schoolHolidays: 0,
        isSynced: stats.length > 0
      };

      stats.forEach(stat => {
        status.totalHolidays += stat.count;

        if (stat.holiday_type === HolidayService.HOLIDAY_TYPES.LOCAL) {
          status.philippineHolidays = stat.count;
        } else if (stat.holiday_type === HolidayService.HOLIDAY_TYPES.INTERNATIONAL) {
          status.internationalHolidays = stat.count;
        } else if (stat.holiday_type === HolidayService.HOLIDAY_TYPES.SCHOOL) {
          status.schoolHolidays = stat.count;
        }

        // Get the most recent sync time
        if (!status.lastSync || new Date(stat.last_update) > new Date(status.lastSync)) {
          status.lastSync = stat.last_update;
        }
      });

      return status;
    } catch (error) {
      logger.error(`Failed to get holiday sync status for ${year}:`, error);
      throw error;
    }
  }
}

// Create and export service instance
const holidayService = new HolidayService();
module.exports = holidayService;
