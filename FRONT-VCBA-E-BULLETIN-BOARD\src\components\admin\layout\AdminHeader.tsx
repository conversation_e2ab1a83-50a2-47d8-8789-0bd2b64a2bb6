import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../../contexts/AdminAuthContext';
import NotificationBell from '../NotificationBell';
import { BarChart3, Calendar, Newspaper, Users, Settings, School, Menu, User, LogOut, Rss, CalendarDays } from 'lucide-react';

interface AdminHeaderProps {
  onToggleSidebar: () => void;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({ onToggleSidebar }) => {
  const { user, logout } = useAdminAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getCurrentTime = () => {
    return new Date().toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPageInfo = () => {
    const path = location.pathname;

    switch (path) {
      case '/admin':
      case '/admin/dashboard':
        return {
          title: 'Dashboard',
          subtitle: 'Overview & Analytics',
          icon: BarChart3,
          description: 'Welcome to your admin dashboard'
        };
      case '/admin/newsfeed':
        return {
          title: 'Admin Newsfeed',
          subtitle: 'Monitor & Engage',
          icon: Rss,
          description: 'Monitor announcements, events, and community engagement'
        };
      case '/admin/calendar':
        return {
          title: 'Calendar & Events',
          subtitle: 'Schedule Management',
          icon: Calendar,
          description: 'Manage academic calendar, events, and announcements'
        };
      case '/admin/holidays':
        return {
          title: 'Holiday Management',
          subtitle: 'System Holidays',
          icon: CalendarDays,
          description: 'Manage automatic holiday synchronization and display settings'
        };
      case '/admin/posts':
        return {
          title: 'Post Management',
          subtitle: 'Content Publishing',
          icon: Newspaper,
          description: 'Create and manage announcements, news, and bulletin posts'
        };
      case '/admin/student-management':
        return {
          title: 'Student Management',
          subtitle: 'User Administration',
          icon: Users,
          description: 'Manage student accounts, profiles, and academic information'
        };
      case '/admin/settings':
        return {
          title: 'Settings',
          subtitle: 'System Configuration',
          icon: Settings,
          description: 'Manage your profile, system preferences, and security settings'
        };
      default:
        return {
          title: 'Admin Panel',
          subtitle: 'VCBA E-Bulletin Board',
          icon: School,
          description: 'Villamor College of Business and Arts, Inc.'
        };
    }
  };

  const pageInfo = getPageInfo();

  return (
    <header style={{
      background: 'white',
      borderBottom: '1px solid #e8f5e8',
      padding: '1rem 2rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
      position: 'sticky',
      top: 0,
      zIndex: 100
    }}>
      {/* Left Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        {/* Sidebar Toggle */}
        <button
          onClick={onToggleSidebar}
          style={{
            background: 'none',
            border: 'none',
            padding: '0.5rem',
            borderRadius: '8px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'background-color 0.2s ease',
            fontSize: '1.25rem'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#f3f4f6';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = 'none';
          }}
        >
          <Menu size={20} color="#2d5016" />
        </button>

        {/* Page Title */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.25rem' }}>
            <pageInfo.icon size={24} color="#2d5016" />
            <h1 style={{
              margin: 0,
              color: '#2d5016',
              fontSize: '1.5rem',
              fontWeight: '700'
            }}>
              {pageInfo.title}
            </h1>
            <span style={{
              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',
              color: 'white',
              padding: '0.25rem 0.75rem',
              borderRadius: '12px',
              fontSize: '0.75rem',
              fontWeight: '600'
            }}>
              {pageInfo.subtitle}
            </span>
          </div>
          <p style={{
            margin: 0,
            color: '#6b7280',
            fontSize: '0.875rem',
            marginBottom: '0.25rem'
          }}>
            {pageInfo.description}
          </p>
          <p style={{
            margin: 0,
            color: '#9ca3af',
            fontSize: '0.75rem'
          }}>
            {getCurrentTime()}
          </p>
        </div>
      </div>

      {/* Right Section */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
        {/* Notifications */}
        <NotificationBell />

        {/* User Profile */}
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            style={{
              background: 'none',
              border: 'none',
              padding: '0.5rem',
              borderRadius: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem',
              transition: 'background-color 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              if (!showUserMenu) {
                e.currentTarget.style.background = 'none';
              }
            }}
          >
            {/* Avatar */}
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #22c55e 0%, #facc15 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: '600',
              fontSize: '1rem'
            }}>
              {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
            </div>

            {/* User Info */}
            <div style={{ textAlign: 'left' }}>
              <div style={{
                color: '#2d5016',
                fontWeight: '600',
                fontSize: '0.9rem',
                lineHeight: '1.2'
              }}>
                {user?.firstName} {user?.lastName}
              </div>
              <div style={{
                color: '#6b7280',
                fontSize: '0.75rem',
                lineHeight: '1.2'
              }}>
                {user?.position || 'Administrator'}
              </div>
            </div>

            {/* Dropdown Arrow */}
            <span style={{
              color: '#6b7280',
              fontSize: '0.75rem',
              transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease'
            }}>
              ▼
            </span>
          </button>

          {/* User Dropdown Menu */}
          {showUserMenu && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '0.5rem',
              background: 'white',
              borderRadius: '12px',
              boxShadow: '0 10px 40px rgba(0, 0, 0, 0.15)',
              border: '1px solid #e8f5e8',
              minWidth: '200px',
              zIndex: 1000
            }}>
              <div style={{ padding: '1rem' }}>
                <div style={{
                  color: '#2d5016',
                  fontWeight: '600',
                  marginBottom: '0.25rem'
                }}>
                  {user?.firstName} {user?.lastName}
                </div>
                <div style={{
                  color: '#6b7280',
                  fontSize: '0.875rem',
                  marginBottom: '1rem'
                }}>
                  {user?.email}
                </div>
                
                <hr style={{
                  border: 'none',
                  borderTop: '1px solid #e8f5e8',
                  margin: '1rem 0'
                }} />

                <button
                  onClick={() => {
                    setShowUserMenu(false);
                    navigate('/admin/settings');
                  }}
                  style={{
                    width: '100%',
                    background: 'none',
                    border: 'none',
                    padding: '0.75rem',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    color: '#374151',
                    fontSize: '0.875rem',
                    marginBottom: '0.5rem',
                    transition: 'background-color 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#f3f4f6';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'none';
                  }}
                >
                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <User size={16} color="#6b7280" />
                    Profile Settings
                  </span>
                </button>

                <button
                  onClick={() => {
                    setShowUserMenu(false);
                    handleLogout();
                  }}
                  style={{
                    width: '100%',
                    background: 'none',
                    border: 'none',
                    padding: '0.75rem',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    textAlign: 'left',
                    color: '#dc2626',
                    fontSize: '0.875rem',
                    transition: 'background-color 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = '#fef2f2';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'none';
                  }}
                >
                  <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <LogOut size={16} color="#ef4444" />
                    Logout
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close menu */}
      {showUserMenu && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999
          }}
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </header>
  );
};

export default AdminHeader;
