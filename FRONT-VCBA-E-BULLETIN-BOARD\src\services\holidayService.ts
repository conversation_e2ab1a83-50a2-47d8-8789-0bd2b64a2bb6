import { apiClient } from './apiClient';

export interface Holiday {
  calendar_id: number;
  title: string;
  description: string;
  event_date: string;
  end_date: string;
  is_active: boolean;
  is_published: boolean;
  is_holiday: boolean;
  holiday_type: 'local' | 'international' | 'school';
  country_code?: string;
  is_auto_generated: boolean;
  api_source?: string;
  local_name?: string;
  holiday_types?: string;
  is_global?: boolean;
  is_fixed?: boolean;
  created_at: string;
  updated_at: string;
}

export interface HolidaySyncStatus {
  year: number;
  lastSync: string | null;
  totalHolidays: number;
  philippineHolidays: number;
  internationalHolidays: number;
  schoolHolidays: number;
  isSynced: boolean;
}

export interface HolidaySyncResult {
  year: number;
  philippineHolidays: number;
  internationalHolidays: number;
  totalInserted: number;
  clearedCount: number;
  errors: string[];
}

export interface HolidayPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface HolidaysResponse {
  holidays: Holiday[];
  pagination: HolidayPagination;
}

export interface HolidayStatistics {
  [year: number]: HolidaySyncStatus;
}

class HolidayService {
  private baseUrl = '/api/holidays';

  /**
   * Get holiday sync status for a specific year
   */
  async getHolidaySyncStatus(year: number): Promise<{ success: boolean; data: HolidaySyncStatus }> {
    const response = await apiClient.get(`${this.baseUrl}/status/${year}`);
    return response.data;
  }

  /**
   * Sync holidays for a specific year
   */
  async syncHolidays(
    year: number,
    options: {
      includePhilippine?: boolean;
      includeInternational?: boolean;
      clearExisting?: boolean;
    } = {}
  ): Promise<{ success: boolean; data: HolidaySyncResult }> {
    const response = await apiClient.post(`${this.baseUrl}/sync/${year}`, {
      includePhilippine: options.includePhilippine ?? true,
      includeInternational: options.includeInternational ?? true,
      clearExisting: options.clearExisting ?? true
    });
    return response.data;
  }

  /**
   * Get holidays for a specific year with pagination and filters
   */
  async getHolidays(
    year: number,
    queryParams?: string
  ): Promise<{ success: boolean; data: HolidaysResponse }> {
    const url = queryParams 
      ? `${this.baseUrl}/${year}?${queryParams}`
      : `${this.baseUrl}/${year}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Toggle holiday active status
   */
  async toggleHolidayStatus(
    holidayId: number,
    isActive: boolean
  ): Promise<{ success: boolean; data: { holidayId: number; is_active: boolean } }> {
    const response = await apiClient.patch(`${this.baseUrl}/${holidayId}/status`, {
      is_active: isActive
    });
    return response.data;
  }

  /**
   * Clear holidays for a specific year
   */
  async clearHolidays(
    year: number,
    holidayType?: string
  ): Promise<{ success: boolean; data: { year: number; clearedCount: number; holiday_type: string } }> {
    const body = holidayType ? { holiday_type: holidayType } : {};
    const response = await apiClient.delete(`${this.baseUrl}/${year}`, { data: body });
    return response.data;
  }

  /**
   * Get holiday statistics for multiple years
   */
  async getHolidayStatistics(): Promise<{ success: boolean; data: HolidayStatistics }> {
    const response = await apiClient.get(`${this.baseUrl}/statistics`);
    return response.data;
  }

  /**
   * Sync holidays for current year (convenience method)
   */
  async syncCurrentYear(options?: {
    includePhilippine?: boolean;
    includeInternational?: boolean;
    clearExisting?: boolean;
  }): Promise<{ success: boolean; data: HolidaySyncResult }> {
    const currentYear = new Date().getFullYear();
    return this.syncHolidays(currentYear, options);
  }

  /**
   * Get holidays for current year (convenience method)
   */
  async getCurrentYearHolidays(queryParams?: string): Promise<{ success: boolean; data: HolidaysResponse }> {
    const currentYear = new Date().getFullYear();
    return this.getHolidays(currentYear, queryParams);
  }

  /**
   * Check if holidays are synced for a year
   */
  async isYearSynced(year: number): Promise<boolean> {
    try {
      const response = await this.getHolidaySyncStatus(year);
      return response.data.isSynced;
    } catch (error) {
      console.error(`Failed to check sync status for ${year}:`, error);
      return false;
    }
  }

  /**
   * Get holiday type display name
   */
  getHolidayTypeDisplayName(type: string): string {
    switch (type) {
      case 'local':
        return 'Philippine Holiday';
      case 'international':
        return 'International Holiday';
      case 'school':
        return 'School Holiday';
      default:
        return 'Holiday';
    }
  }

  /**
   * Get holiday type icon
   */
  getHolidayTypeIcon(type: string): string {
    switch (type) {
      case 'local':
        return '🇵🇭';
      case 'international':
        return '🌍';
      case 'school':
        return '🏫';
      default:
        return '🎉';
    }
  }

  /**
   * Get holiday type color
   */
  getHolidayTypeColor(type: string): string {
    switch (type) {
      case 'local':
        return '#dc2626'; // Red
      case 'international':
        return '#7c3aed'; // Purple
      case 'school':
        return '#059669'; // Green
      default:
        return '#6b7280'; // Gray
    }
  }

  /**
   * Format holiday date for display
   */
  formatHolidayDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Check if a holiday is read-only (auto-generated)
   */
  isHolidayReadOnly(holiday: Holiday): boolean {
    return holiday.is_holiday && holiday.is_auto_generated;
  }

  /**
   * Get years available for holiday management
   */
  getAvailableYears(): number[] {
    const currentYear = new Date().getFullYear();
    const years: number[] = [];
    
    // 5 years back to 10 years forward
    for (let i = -5; i <= 10; i++) {
      years.push(currentYear + i);
    }
    
    return years;
  }

  /**
   * Validate year for holiday operations
   */
  validateYear(year: number): boolean {
    const currentYear = new Date().getFullYear();
    return year >= currentYear - 5 && year <= currentYear + 10;
  }
}

// Export singleton instance
export const holidayService = new HolidayService();
export default holidayService;
