const https = require('https');

function fetchHolidays(countryCode, year) {
  return new Promise((resolve, reject) => {
    const url = `https://date.nager.at/api/v3/publicholidays/${year}/${countryCode}`;
    
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const holidays = JSON.parse(data);
          resolve(holidays);
        } catch (error) {
          reject(error);
        }
      });
    }).on('error', reject);
  });
}

async function findCommonHolidays() {
  try {
    const countries = ['PH', 'US', 'GB'];
    const allHolidays = {};
    
    for (const country of countries) {
      console.log(`Fetching holidays for ${country}...`);
      const holidays = await fetchHolidays(country, 2025);
      allHolidays[country] = holidays;
    }
    
    // Find common holiday names
    const holidayNames = new Map();
    
    Object.entries(allHolidays).forEach(([country, holidays]) => {
      holidays.forEach(holiday => {
        const name = holiday.name;
        if (!holidayNames.has(name)) {
          holidayNames.set(name, { countries: [], dates: [], examples: [] });
        }
        holidayNames.get(name).countries.push(country);
        holidayNames.get(name).dates.push(holiday.date);
        holidayNames.get(name).examples.push({ country, date: holiday.date, localName: holiday.localName });
      });
    });
    
    // Filter for holidays that appear in multiple countries (international)
    console.log('\n=== INTERNATIONALLY RECOGNIZED HOLIDAYS ===');
    const internationalHolidays = [];
    
    holidayNames.forEach((data, name) => {
      if (data.countries.length >= 2) {
        internationalHolidays.push({
          name,
          countries: data.countries.join(', '),
          count: data.countries.length,
          examples: data.examples.slice(0, 3)
        });
      }
    });
    
    // Sort by popularity (number of countries)
    internationalHolidays.sort((a, b) => b.count - a.count);
    
    console.table(internationalHolidays);
    
    // Show specific holidays we want
    console.log('\n=== SPECIFIC HOLIDAYS WE WANT ===');
    const wantedHolidays = [
      'New Year\'s Day', 'Christmas Day', 'Good Friday', 'Easter Sunday', 
      'Labor Day', 'Valentine\'s Day', 'Halloween', 'Mother\'s Day', 'Father\'s Day'
    ];
    
    wantedHolidays.forEach(wanted => {
      const found = internationalHolidays.find(h => h.name.includes(wanted.replace('\'', '')));
      if (found) {
        console.log(`✅ ${wanted}: Found in ${found.count} countries (${found.countries})`);
      } else {
        console.log(`❌ ${wanted}: Not found in common holidays`);
      }
    });
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

findCommonHolidays();
