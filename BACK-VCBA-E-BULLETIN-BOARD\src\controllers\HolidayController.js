const asyncHandler = require('../middleware/asyncHandler');
const holidayService = require('../services/HolidayService');
const database = require('../config/database');
const logger = require('../utils/logger');

/**
 * Holiday Controller
 * Handles admin operations for holiday management
 */
class HolidayController {
  // Get holiday sync status for a year
  getHolidaySyncStatus = asyncHandler(async (req, res) => {
    const { year } = req.params;
    
    if (!year || isNaN(parseInt(year))) {
      return res.status(400).json({
        success: false,
        message: 'Valid year is required'
      });
    }

    const status = await holidayService.getHolidaySyncStatus(parseInt(year));

    res.status(200).json({
      success: true,
      message: 'Holiday sync status retrieved successfully',
      data: status
    });
  });

  // Sync holidays for a specific year
  syncHolidays = asyncHandler(async (req, res) => {
    const { year } = req.params;
    const {
      includePhilippine = true,
      includeInternational = true,
      clearExisting = true
    } = req.body;

    if (!year || isNaN(parseInt(year))) {
      return res.status(400).json({
        success: false,
        message: 'Valid year is required'
      });
    }

    const parsedYear = parseInt(year);
    const currentYear = new Date().getFullYear();
    
    // Validate year range (5 years back, 10 years forward)
    if (parsedYear < currentYear - 5 || parsedYear > currentYear + 10) {
      return res.status(400).json({
        success: false,
        message: `Year must be between ${currentYear - 5} and ${currentYear + 10}`
      });
    }

    logger.info(`Admin ${req.user.id} initiated holiday sync for ${parsedYear}`, {
      includePhilippine,
      includeInternational,
      clearExisting
    });

    const results = await holidayService.syncHolidays(parsedYear, {
      includePhilippine,
      includeInternational,
      clearExisting
    });

    res.status(200).json({
      success: true,
      message: `Holiday sync completed for ${parsedYear}`,
      data: results
    });
  });

  // Get holidays for a specific year with admin details
  getHolidays = asyncHandler(async (req, res) => {
    const { year } = req.params;
    const { 
      page = 1, 
      limit = 50,
      holiday_type,
      is_active 
    } = req.query;

    if (!year || isNaN(parseInt(year))) {
      return res.status(400).json({
        success: false,
        message: 'Valid year is required'
      });
    }

    let whereConditions = ['YEAR(event_date) = ?', 'is_holiday = 1'];
    let params = [parseInt(year)];

    if (holiday_type) {
      whereConditions.push('holiday_type = ?');
      params.push(holiday_type);
    }

    if (is_active !== undefined) {
      whereConditions.push('is_active = ?');
      params.push(parseInt(is_active));
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    const sql = `
      SELECT 
        calendar_id,
        title,
        description,
        event_date,
        end_date,
        is_active,
        is_published,
        is_holiday,
        holiday_type,
        country_code,
        is_auto_generated,
        api_source,
        local_name,
        holiday_types,
        is_global,
        is_fixed,
        created_at,
        updated_at
      FROM school_calendar 
      ${whereClause}
      AND deleted_at IS NULL
      ORDER BY event_date ASC
      LIMIT ? OFFSET ?
    `;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    params.push(parseInt(limit), offset);

    const holidays = await database.query(sql, params);

    // Get total count
    const countSql = `
      SELECT COUNT(*) as total 
      FROM school_calendar 
      ${whereClause}
      AND deleted_at IS NULL
    `;
    
    const countResult = await database.query(countSql, params.slice(0, -2));
    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Holidays retrieved successfully',
      data: {
        holidays,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    });
  });

  // Toggle holiday active status
  toggleHolidayStatus = asyncHandler(async (req, res) => {
    const { holidayId } = req.params;
    const { is_active } = req.body;

    if (!holidayId || isNaN(parseInt(holidayId))) {
      return res.status(400).json({
        success: false,
        message: 'Valid holiday ID is required'
      });
    }

    if (is_active === undefined || typeof is_active !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'is_active must be a boolean value'
      });
    }

    // Check if holiday exists and is auto-generated
    const holiday = await database.findOne(
      'SELECT calendar_id, title, is_holiday, is_auto_generated FROM school_calendar WHERE calendar_id = ? AND deleted_at IS NULL',
      [parseInt(holidayId)]
    );

    if (!holiday) {
      return res.status(404).json({
        success: false,
        message: 'Holiday not found'
      });
    }

    if (!holiday.is_holiday) {
      return res.status(400).json({
        success: false,
        message: 'This is not a holiday event'
      });
    }

    // Update the holiday status
    const result = await database.update(
      'school_calendar',
      { 
        is_active: is_active ? 1 : 0,
        updated_at: new Date()
      },
      'calendar_id = ?',
      [parseInt(holidayId)]
    );

    if (result.affectedRows === 0) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update holiday status'
      });
    }

    logger.info(`Admin ${req.user.id} ${is_active ? 'enabled' : 'disabled'} holiday: ${holiday.title} (ID: ${holidayId})`);

    res.status(200).json({
      success: true,
      message: `Holiday ${is_active ? 'enabled' : 'disabled'} successfully`,
      data: {
        holidayId: parseInt(holidayId),
        is_active
      }
    });
  });

  // Clear holidays for a specific year
  clearHolidays = asyncHandler(async (req, res) => {
    const { year } = req.params;
    const { holiday_type } = req.body;

    if (!year || isNaN(parseInt(year))) {
      return res.status(400).json({
        success: false,
        message: 'Valid year is required'
      });
    }

    const parsedYear = parseInt(year);
    let whereConditions = ['YEAR(event_date) = ?', 'is_auto_generated = 1', 'is_holiday = 1', 'deleted_at IS NULL'];
    let params = [parsedYear];

    if (holiday_type) {
      whereConditions.push('holiday_type = ?');
      params.push(holiday_type);
    }

    const sql = `
      UPDATE school_calendar 
      SET deleted_at = NOW() 
      WHERE ${whereConditions.join(' AND ')}
    `;

    const result = await database.query(sql, params);
    const clearedCount = result.affectedRows || 0;

    logger.info(`Admin ${req.user.id} cleared ${clearedCount} holidays for ${parsedYear}${holiday_type ? ` (type: ${holiday_type})` : ''}`);

    res.status(200).json({
      success: true,
      message: `Cleared ${clearedCount} holidays for ${parsedYear}`,
      data: {
        year: parsedYear,
        clearedCount,
        holiday_type: holiday_type || 'all'
      }
    });
  });

  // Get holiday statistics
  getHolidayStatistics = asyncHandler(async (req, res) => {
    const currentYear = new Date().getFullYear();
    const years = [currentYear - 1, currentYear, currentYear + 1];
    
    const statistics = {};

    for (const year of years) {
      const status = await holidayService.getHolidaySyncStatus(year);
      statistics[year] = status;
    }

    res.status(200).json({
      success: true,
      message: 'Holiday statistics retrieved successfully',
      data: statistics
    });
  });
}

module.exports = new HolidayController();
